#Files in the config/locales directory are used for internationalization
#and are automatically loaded by Rails. If you want to use locales other
#than English, add the necessary files in this directory.
#To use the locales, use `I18n.t`:
#I18n.t 'hello'
#In views, this is aliased to just `t`:
#<%= t('hello') %>
#To use a different locale, set it with `I18n.locale`:
#I18n.locale = :es
#This would use the information in config/locales/es.yml.
#The following keys must be escaped otherwise they will not be retrieved by
#the default I18n backend:
#true, false, on, off, yes, no
#Instead, surround them with single quotes.
#en:
#'true': 'foo'
#To learn more, please read the Rails Internationalization guide
#available at https://guides.rubyonrails.org/i18n.html.
sr-Latn:
  hello: 'Zdravo svete'
  messages:
    reset_password_success: Opa! Zahtev za resetovanjem lozinke je uspešan. Proverite vašu e-poštu za uputstvo.
    reset_password_failure: O ne! Nismo mogli da pronađemo nijednog korisnika sa navedenom e-poštom.
    inbox_deletetion_response: Your inbox deletion request will be processed in some time.
  errors:
    validations:
      presence: ne sme biti prazno
    webhook:
      invalid: Neispravni događaji
    signup:
      disposable_email: Ne dozvoljavamo potrošne e-pošte
      blocked_domain: This domain is not allowed. If you believe this is a mistake, please contact support.
      invalid_email: Uneli ste neispravnu e-poštu
      email_already_exists: 'Već ste registrovali nalog sa %{email}'
      invalid_params: 'Invalid, please check the signup paramters and try again'
      failed: Registracija nije uspela
    data_import:
      data_type:
        invalid: Neispravan tip podatka
    contacts:
      import:
        failed: Datoteka je prazna
      export:
        success: We will notify you once contacts export file is ready to view.
      email:
        invalid: Neispravna e-pošta
      phone_number:
        invalid: treba biti u e164 formatu
    categories:
      locale:
        unique: treba biti jedinstvena u kategoriji i portalu
    dyte:
      invalid_message_type: 'Invalid message type. Action not permitted'
    slack:
      invalid_channel_id: 'Invalid slack channel. Please try again'
    inboxes:
      imap:
        socket_error: Molim vas proverite vezu sa mrežom, IMAP adresu i pokušajte ponovo.
        no_response_error: Molim vas proverite IMAP kredencijale i pokušajte ponovo.
        host_unreachable_error: Domaćin je nedostupan, Molim vas proverite IMAP adresu, IMAP port i pokušajte ponovo.
        connection_timed_out_error: Vreme za povezivanje je isteklo za %{address}:%{port}
        connection_closed_error: Povezivanje je prekinuto.
      validations:
        name: ne treba početi ili se završiti sa simbolima i ne treba da sadrži < > / \ @ karaktere.
    custom_filters:
      number_of_records: Limit reached. The maximum number of allowed custom filters for a user per account is 50.
      invalid_attribute: Invalid attribute key - [%{key}]. The key should be one of [%{allowed_keys}] or a custom attribute defined in the account.
      invalid_operator: Invalid operator. The allowed operators for %{attribute_name} are [%{allowed_keys}].
      invalid_query_operator: Query operator must be either "AND" or "OR".
      invalid_value: Invalid value. The values provided for %{attribute_name} are invalid
    custom_attribute_definition:
      key_conflict: The provided key is not allowed as it might conflict with default attributes.
  reports:
    period: Period izveštaja %{since} do %{until}
    utc_warning: Generisani izveštaj je u UTC vremenskoj zoni
    agent_csv:
      agent_name: Naziv agenta
      conversations_count: Assigned conversations
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
      resolution_count: Broj rešenih
      avg_customer_waiting_time: Avg customer waiting time
    inbox_csv:
      inbox_name: Naziv prijemnog sandučeta
      inbox_type: Vrsta prijemnog sandučeta
      conversations_count: Broj razgovora
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
    label_csv:
      label_title: Oznaka
      conversations_count: Broj razgovora
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
    team_csv:
      team_name: Naziv tima
      conversations_count: Broj razgovora
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
      resolution_count: Broj rešenih
      avg_customer_waiting_time: Avg customer waiting time
    conversation_traffic_csv:
      timezone: Timezone
    sla_csv:
      conversation_id: Conversation ID
      sla_policy_breached: SLA Policy
      assignee: Assignee
      team: Tim
      inbox: Prijemno sanduče
      labels: Oznake
      conversation_link: Link to the Conversation
      breached_events: Breached Events
    default_group_by: dan
    csat:
      headers:
        contact_name: Naziv kontakta
        contact_email_address: Adresa e-pošte kontakta
        contact_phone_number: Broj telefona kontakta
        link_to_the_conversation: Veza ka razgovoru
        agent_name: Ime agenta
        rating: Ocena
        feedback: Komentar povratne informacije
        recorded_at: Snimljen dana
  notifications:
    notification_title:
      conversation_creation: 'A conversation (#%{display_id}) has been created in %{inbox_name}'
      conversation_assignment: 'A conversation (#%{display_id}) has been assigned to you'
      assigned_conversation_new_message: 'A new message is created in conversation (#%{display_id})'
      conversation_mention: 'You have been mentioned in conversation (#%{display_id})'
      sla_missed_first_response: 'SLA target first response missed for conversation (#%{display_id})'
      sla_missed_next_response: 'SLA target next response missed for conversation (#%{display_id})'
      sla_missed_resolution: 'SLA target resolution missed for conversation (#%{display_id})'
    attachment: 'Attachment'
    no_content: 'No content'
  conversations:
    captain:
      handoff: 'Transferring to another agent for further assistance.'
    messages:
      instagram_story_content: '%{story_sender} vas je pomenuo u priči: '
      instagram_deleted_story_content: Ova priča više nije dostupna.
      deleted: Poruka je obrisana
      delivery_status:
        error_code: 'Error code: %{error_code}'
    activity:
      captain:
        resolved: 'Conversation was marked resolved by %{user_name} due to inactivity'
        open: 'Conversation was marked open by %{user_name}'
      status:
        resolved: 'Razgovor je označen kao rešen od strane %{user_name}'
        contact_resolved: 'Razgovor je rešen od strane %{contact_name}'
        open: 'Razgovor je ponovo otvoren od strane %{user_name}'
        pending: 'Razgovor je označen kao na čekanju od strane %{user_name}'
        snoozed: 'Razgovor je odložen od strane %{user_name}'
        auto_resolved_days: 'Razgovor je označen kao rešen od strane sistema zbog %{count} dana neaktivnosti'
        auto_resolved_hours: 'Conversation was marked resolved by system due to %{count} hours of inactivity'
        auto_resolved_minutes: 'Conversation was marked resolved by system due to %{count} minutes of inactivity'
        system_auto_open: System reopened the conversation due to a new incoming message.
      priority:
        added: '%{user_name} set the priority to %{new_priority}'
        updated: '%{user_name} changed the priority from %{old_priority} to %{new_priority}'
        removed: '%{user_name} removed the priority'
      assignee:
        self_assigned: '%{user_name} je samododelio sebe ovom razgovoru'
        assigned: 'Dodeljeno %{assignee_name} od strane %{user_name}'
        removed: 'Razgovoru uklonjena dodela od strane %{user_name}'
      team:
        assigned: 'Dodeljeno %{team_name} od strane %{user_name}'
        assigned_with_assignee: 'Dodeljeno %{assignee_name} putem %{team_name} od strane %{user_name}'
        removed: 'Uklonjena dodela %{team_name} od strane %{user_name}'
      labels:
        added: '%{user_name} je dodao %{labels}'
        removed: '%{user_name} je uklonio %{labels}'
      sla:
        added: '%{user_name} added SLA policy %{sla_name}'
        removed: '%{user_name} removed SLA policy %{sla_name}'
      muted: '%{user_name} je utišao razgovor'
      unmuted: '%{user_name} je uklonio utišanje razgovora'
      auto_resolution_message: 'Resolving the conversation as it has been inactive for a while. Please start a new conversation if you need further assistance.'
    templates:
      greeting_message_body: '%{account_name} obično odgovara za nekoliko sati.'
      ways_to_reach_you_message_body: 'Pružite timu način da vas kontaktira.'
      email_input_box_message_body: 'Budite obavešteni putem e-poruke'
      csat_input_message_body: 'Molim vas ocenite ovaj razgovor'
    reply:
      email:
        header:
          from_with_name: '%{assignee_name} iz %{inbox_name} <%{from_email}>'
          reply_with_name: '%{assignee_name} iz %{inbox_name} <reply+%{reply_email}>'
          friendly_name: '%{sender_name} iz %{business_name} <%{from_email}>'
          professional_name: '%{business_name} <%{from_email}>'
      channel_email:
        header:
          reply_with_name: '%{assignee_name} iz %{inbox_name} <%{from_email}>'
          reply_with_inbox_name: '%{inbox_name} <%{from_email}>'
      email_subject: 'Nove poruke u ovom razgovoru'
      transcript_subject: 'Transkript razgovora'
    survey:
      response: 'Molim vas ocenite ovaj razgovor, %{link}'
  contacts:
    online:
      delete: '%{contact_name} je dostupan, molim vas pokušajte ponovo'
  integration_apps:
    dashboard_apps:
      name: 'Aplikacije radne table'
      description: 'Dashboard Apps allow you to create and embed applications that display user information, orders, or payment history, providing more context to your customer support agents.'
    dyte:
      name: 'Dyte'
      description: 'Dyte is a product that integrates audio and video functionalities into your application. With this integration, your agents can start video/voice calls with your customers directly from Livechat.'
      meeting_name: '%{agent_name} has started a meeting'
    slack:
      name: 'Slack'
      description: "Integrate Livechat with Slack to keep your team in sync. This integration allows you to receive notifications for new conversations and respond to them directly within Slack's interface."
    webhooks:
      name: 'Veb zakačke'
      description: 'Webhook events provide real-time updates about activities in your Livechat account. You can subscribe to your preferred events, and Livechat will send you HTTP callbacks with the updates.'
    dialogflow:
      name: 'Dialogflow'
      description: 'Build chatbots with Dialogflow and easily integrate them into your inbox. These bots can handle initial queries before transferring them to a customer service agent.'
    google_translate:
      name: 'Google Translate'
      description: "Integrate Google Translate to help agents easily translate customer messages. This integration automatically detects the language and converts it to the agent's or admin's preferred language."
    openai:
      name: 'OpenAI'
      description: 'Leverage the power of large language models from OpenAI with the features such as reply suggestions, summarization, message rephrasing, spell-checking, and label classification.'
    linear:
      name: 'Linear'
      description: 'Create issues in Linear directly from your conversation window. Alternatively, link existing Linear issues for a more streamlined and efficient issue tracking process.'
    shopify:
      name: 'Shopify'
      description: 'Connect your Shopify store to access order details, customer information, and product data directly within your conversations and helps your support team provide faster, more contextual assistance to your customers.'
    leadsquared:
      name: 'LeadSquared'
      short_description: 'Sync your contacts and conversations with LeadSquared CRM.'
      description: 'Sync your contacts and conversations with LeadSquared CRM. This integration automatically creates leads in LeadSquared when new contacts are added, and logs conversation activity to provide your sales team with complete context.'
  captain:
    copilot_error: 'Please connect an assistant to this inbox to use Copilot'
    copilot_limit: 'You are out of Copilot credits. You can buy more credits from the billing section.'
  public_portal:
    search:
      search_placeholder: Search for article by title or body...
      empty_placeholder: Ništa nije pronađeno.
      loading_placeholder: Searching...
      results_title: Search results
    toc_header: 'On this page'
    hero:
      sub_title: Search for the articles here or browse the categories below.
    common:
      home: Početak
      last_updated_on: Last updated on %{last_updated_on}
      view_all_articles: View all
      article: article
      articles: članci
      author: autor
      authors: authors
      other: other
      others: others
      by: By
      no_articles: There are no articles here
    footer:
      made_with: Made with
    header:
      go_to_homepage: Website
      appearance:
        system: System
        light: Light
        dark: Dark
      featured_articles: Featured Articles
      uncategorized: Uncategorized
    404:
      title: Page not found
      description: We couldn't find the page you were looking for.
      back_to_home: Go to home page
  slack_unfurl:
    fields:
      name: Ime
      email: E-pošta
      phone_number: Phone
      company_name: Firma
      inbox_name: Prijemno sanduče
      inbox_type: Inbox Type
    button: Otvoren razgovor
  time_units:
    days:
      one: '%{count} day'
      few: '%{count} days'
      other: '%{count} days'
    hours:
      one: '%{count} hour'
      few: '%{count} hours'
      other: '%{count} hours'
    minutes:
      one: '%{count} minute'
      few: '%{count} minutes'
      other: '%{count} minutes'
    seconds:
      one: '%{count} second'
      few: '%{count} seconds'
      other: '%{count} seconds'
  automation:
    system_name: 'Automation System'
  crm:
    no_message: 'No messages in conversation'
    attachment: '[Attachment: %{type}]'
    no_content: '[No content]'
    created_activity: |
      New conversation started on %{brand_name}

      Channel: %{channel_info}
      Created: %{formatted_creation_time}
      Conversation ID: %{display_id}
      View in %{brand_name}: %{url}
    transcript_activity: |
      Conversation Transcript from %{brand_name}

      Channel: %{channel_info}
      Conversation ID: %{display_id}
      View in %{brand_name}: %{url}

      Transcript:
      %{format_messages}
