require 'administrate/base_dashboard'

class AgentBotDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    access_token: Field::HasOne,
    avatar_url: AvatarField,
    avatar: Field::ActiveStorage.with_options(
      destroy_url: proc do |_namespace, _resource, attachment|
        [:avatar_super_admin_agent_bot, { attachment_id: attachment.id }]
      end
    ),
    id: Field::Number,
    name: Field::String,
    account: Field::BelongsTo.with_options(searchable: true, searchable_field: 'name', order: 'id DESC'),
    description: Field::String,
    outgoing_url: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    avatar_url
    account
    name
    outgoing_url
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    avatar_url
    account
    name
    description
    outgoing_url
    access_token
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    name
    avatar
    account
    description
    outgoing_url
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how agent bots are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(agent_bot)
  #   "AgentBot ##{agent_bot.id}"
  # end
end
