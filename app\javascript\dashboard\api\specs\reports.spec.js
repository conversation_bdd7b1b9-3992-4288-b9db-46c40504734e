import reportsAPI from '../reports';
import ApiClient from '../ApiClient';

describe('#Reports API', () => {
  it('creates correct instance', () => {
    expect(reportsAPI).toBeInstanceOf(ApiClient);
    expect(reportsAPI.apiVersion).toBe('/api/v2');
    expect(reportsAPI).toHaveProperty('get');
    expect(reportsAPI).toHaveProperty('show');
    expect(reportsAPI).toHaveProperty('create');
    expect(reportsAPI).toHaveProperty('update');
    expect(reportsAPI).toHaveProperty('delete');
    expect(reportsAPI).toHaveProperty('getReports');
    expect(reportsAPI).toHaveProperty('getSummary');
    expect(reportsAPI).toHaveProperty('getAgentReports');
    expect(reportsAPI).toHaveProperty('getLabelReports');
    expect(reportsAPI).toHaveProperty('getInboxReports');
    expect(reportsAPI).toHaveProperty('getTeamReports');
  });
  describe('API calls', () => {
    const originalAxios = window.axios;
    const axiosMock = {
      post: vi.fn(() => Promise.resolve()),
      get: vi.fn(() => Promise.resolve()),
      patch: vi.fn(() => Promise.resolve()),
      delete: vi.fn(() => Promise.resolve()),
    };

    beforeEach(() => {
      window.axios = axiosMock;
    });

    afterEach(() => {
      window.axios = originalAxios;
    });

    it('#getAccountReports', () => {
      reportsAPI.getReports({
        metric: 'conversations_count',
        from: **********,
        to: **********,
      });
      expect(axiosMock.get).toHaveBeenCalledWith('/api/v2/reports', {
        params: {
          metric: 'conversations_count',
          since: **********,
          until: **********,
          type: 'account',
          timezone_offset: -0,
        },
      });
    });

    it('#getAccountSummary', () => {
      reportsAPI.getSummary(**********, **********);
      expect(axiosMock.get).toHaveBeenCalledWith('/api/v2/reports/summary', {
        params: {
          business_hours: undefined,
          group_by: undefined,
          id: undefined,
          since: **********,
          timezone_offset: -0,
          type: 'account',
          until: **********,
        },
      });
    });

    it('#getAgentReports', () => {
      reportsAPI.getAgentReports({
        from: **********,
        to: **********,
        businessHours: true,
      });
      expect(axiosMock.get).toHaveBeenCalledWith('/api/v2/reports/agents', {
        params: {
          since: **********,
          until: **********,
          business_hours: true,
        },
      });
    });

    it('#getLabelReports', () => {
      reportsAPI.getLabelReports({ from: **********, to: ********** });
      expect(axiosMock.get).toHaveBeenCalledWith('/api/v2/reports/labels', {
        params: {
          since: **********,
          until: **********,
        },
      });
    });

    it('#getInboxReports', () => {
      reportsAPI.getInboxReports({ from: **********, to: ********** });
      expect(axiosMock.get).toHaveBeenCalledWith('/api/v2/reports/inboxes', {
        params: {
          since: **********,
          until: **********,
        },
      });
    });

    it('#getTeamReports', () => {
      reportsAPI.getTeamReports({ from: **********, to: ********** });
      expect(axiosMock.get).toHaveBeenCalledWith('/api/v2/reports/teams', {
        params: {
          since: **********,
          until: **********,
        },
      });
    });

    it('#getBotMetrics', () => {
      reportsAPI.getBotMetrics({ from: **********, to: ********** });
      expect(axiosMock.get).toHaveBeenCalledWith(
        '/api/v2/reports/bot_metrics',
        {
          params: {
            since: **********,
            until: **********,
          },
        }
      );
    });

    it('#getBotSummary', () => {
      reportsAPI.getBotSummary({
        from: **********,
        to: **********,
        groupBy: 'date',
        businessHours: true,
      });
      expect(axiosMock.get).toHaveBeenCalledWith(
        '/api/v2/reports/bot_summary',
        {
          params: {
            since: **********,
            until: **********,
            type: 'account',
            group_by: 'date',
            business_hours: true,
          },
        }
      );
    });

    it('#getConversationMetric', () => {
      reportsAPI.getConversationMetric('account');
      expect(axiosMock.get).toHaveBeenCalledWith(
        '/api/v2/reports/conversations',
        {
          params: {
            type: 'account',
            page: 1,
          },
        }
      );
    });
  });
});
