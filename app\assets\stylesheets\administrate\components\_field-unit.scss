.field-unit {
  @include administrate-clearfix;
  align-items: center;
  display: flex;
  margin-bottom: $base-spacing;
  position: relative;
  width: 100%;
}

.field-unit__label {
  float: left;
  margin-left: 0.625rem;
  text-align: right;
  width: calc(15% - 0.625rem);
}

.field-unit__field {
  float: left;
  margin-left: 1.25rem;
  max-width: 31.15rem;
  width: 100%;
}

.field-unit--nested {
  border: $base-border;
  margin-left: 7.5%;
  max-width: 37.5rem;
  padding: $small-spacing;
  width: 100%;

  .field-unit__field {
    width: 100%;
  }

  .field-unit__label {
    width: 10rem;
  }
}

.field-unit--required {
  label::after {
    color: $red;
    content: ' *';
  }
}

.attribute-data--avatar-field {
  height: $space-larger;
  width: $space-larger;

  img {
    border-radius: 50%;
  }
}
