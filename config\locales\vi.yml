#Files in the config/locales directory are used for internationalization
#and are automatically loaded by Rails. If you want to use locales other
#than English, add the necessary files in this directory.
#To use the locales, use `I18n.t`:
#I18n.t 'hello'
#In views, this is aliased to just `t`:
#<%= t('hello') %>
#To use a different locale, set it with `I18n.locale`:
#I18n.locale = :es
#This would use the information in config/locales/es.yml.
#The following keys must be escaped otherwise they will not be retrieved by
#the default I18n backend:
#true, false, on, off, yes, no
#Instead, surround them with single quotes.
#en:
#'true': 'foo'
#To learn more, please read the Rails Internationalization guide
#available at https://guides.rubyonrails.org/i18n.html.
vi:
  hello: 'Chào thế giới'
  messages:
    reset_password_success: Chà! Yêu cầu đặt lại mật khẩu thành công. Kiểm tra thư của bạn để biết hướng dẫn.
    reset_password_failure: Uh ho! Chúng tôi không thể tìm thấy bất kỳ người dùng nào có email được chỉ định.
    inbox_deletetion_response: <PERSON><PERSON><PERSON> cầu xoá hộp thư của bạn sẽ được xử lý.
  errors:
    validations:
      presence: không được để trống
    webhook:
      invalid: Sự kiện không hợp lệ
    signup:
      disposable_email: Chúng tôi không cho phép các email dùng một lần
      blocked_domain: This domain is not allowed. If you believe this is a mistake, please contact support.
      invalid_email: Bạn đã nhập một email không hợp lệ
      email_already_exists: 'Bạn đã đăng ký một tài khoản với %{email}'
      invalid_params: 'Invalid, please check the signup paramters and try again'
      failed: Đăng ký thât bại
    data_import:
      data_type:
        invalid: Kiểu dữ liệu không hợp lệ
    contacts:
      import:
        failed: Chưa chọn tệp
      export:
        success: Chúng tôi sẽ thông báo khi tệp xuất danh sách liên hệ đã sẵn sàng.
      email:
        invalid: Email không hợp lệ
      phone_number:
        invalid: nên theo đinh dạng e164
    categories:
      locale:
        unique: phải là duy nhất trong danh mục và cổng thông tin
    dyte:
      invalid_message_type: 'Loại tin nhắn không hợp lệ. Hành động không được phép'
    slack:
      invalid_channel_id: 'Invalid slack channel. Please try again'
    inboxes:
      imap:
        socket_error: Vui lòng kiểm tra kết nối mạng, địa chỉ IMAP và thử lại.
        no_response_error: Vui lòng kiểm tra thông tin đăng nhập IMAP và thử lại.
        host_unreachable_error: Máy chủ không thể truy cập được, Vui lòng kiểm tra địa chỉ IMAP, cổng IMAP và thử lại.
        connection_timed_out_error: Kết nối đã hết thời gian chờ %{address}:%{port}
        connection_closed_error: Kêt nối bị đóng.
      validations:
        name: không nên bắt đầu hoặc kết thúc bằng các ký hiệu và không nên có kí tự < > / \ @.
    custom_filters:
      number_of_records: Đã đạt giới hạn. Số lượng tuỳ chọn lọc tối đa cho mỗi mỗi người dùng mỗi tài khoản là 50.
      invalid_attribute: Invalid attribute key - [%{key}]. The key should be one of [%{allowed_keys}] or a custom attribute defined in the account.
      invalid_operator: Invalid operator. The allowed operators for %{attribute_name} are [%{allowed_keys}].
      invalid_query_operator: Query operator must be either "AND" or "OR".
      invalid_value: Invalid value. The values provided for %{attribute_name} are invalid
    custom_attribute_definition:
      key_conflict: The provided key is not allowed as it might conflict with default attributes.
  reports:
    period: Thời gian báo cáo từ %{since} đến %{until}
    utc_warning: Báo cáo đã được tạo với múi giờ UTC
    agent_csv:
      agent_name: Tên tổng đài viên
      conversations_count: Assigned conversations
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
      resolution_count: Số lượng giải quyết
      avg_customer_waiting_time: Avg customer waiting time
    inbox_csv:
      inbox_name: Tên kênh
      inbox_type: Kiểu kênh
      conversations_count: Số hội thoại
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
    label_csv:
      label_title: Nhãn
      conversations_count: Số hội thoại
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
    team_csv:
      team_name: Tên nhóm
      conversations_count: Số hội thoại
      avg_first_response_time: Avg first response time
      avg_resolution_time: Avg resolution time
      resolution_count: Số lượng giải quyết
      avg_customer_waiting_time: Avg customer waiting time
    conversation_traffic_csv:
      timezone: Múi giờ
    sla_csv:
      conversation_id: Conversation ID
      sla_policy_breached: SLA Policy
      assignee: Assignee
      team: Nhóm
      inbox: Hộp thư đến
      labels: Nhãn
      conversation_link: Link to the Conversation
      breached_events: Breached Events
    default_group_by: ngày
    csat:
      headers:
        contact_name: Tên liên hệ
        contact_email_address: Địa chỉ email của liên hệ
        contact_phone_number: Số điện thoại của liên hệ
        link_to_the_conversation: Liên kế tới hội thoại
        agent_name: Tên nhà cung cấp
        rating: Đánh giá
        feedback: Bình luận phản hồi
        recorded_at: Ngày nghi
  notifications:
    notification_title:
      conversation_creation: 'A conversation (#%{display_id}) has been created in %{inbox_name}'
      conversation_assignment: 'A conversation (#%{display_id}) has been assigned to you'
      assigned_conversation_new_message: 'A new message is created in conversation (#%{display_id})'
      conversation_mention: 'You have been mentioned in conversation (#%{display_id})'
      sla_missed_first_response: 'SLA target first response missed for conversation (#%{display_id})'
      sla_missed_next_response: 'SLA target next response missed for conversation (#%{display_id})'
      sla_missed_resolution: 'SLA target resolution missed for conversation (#%{display_id})'
    attachment: 'Attachment'
    no_content: 'No content'
  conversations:
    captain:
      handoff: 'Transferring to another agent for further assistance.'
    messages:
      instagram_story_content: '%{story_sender} đã đề cập đến bạn trong hội thoại: '
      instagram_deleted_story_content: Hội thoại này không còn nữa.
      deleted: Tin nhắn đã bị xoá
      delivery_status:
        error_code: 'Error code: %{error_code}'
    activity:
      captain:
        resolved: 'Conversation was marked resolved by %{user_name} due to inactivity'
        open: 'Conversation was marked open by %{user_name}'
      status:
        resolved: 'Cuộc trò chuyện được đánh dấu là đã giải quyết bởi %{user_name}'
        contact_resolved: 'Hội thoại đã được giải quyết bởi %{contact_name}'
        open: 'Cuộc trò chuyện đã được mở lại bởi %{user_name}'
        pending: 'Hội thoại được đánh dấu là chưa giải quyết bởi %{user_name}'
        snoozed: 'Hội thoại đã được tạm dừng lại bởi %{user_name}'
        auto_resolved_days: 'Hội thoại được đánh dấu là đã giải quyết bởi hệ thống vì %{count} ngày không hoạt động'
        auto_resolved_hours: 'Conversation was marked resolved by system due to %{count} hours of inactivity'
        auto_resolved_minutes: 'Conversation was marked resolved by system due to %{count} minutes of inactivity'
        system_auto_open: Hệ thống mở lại hội thoại do có tin nhắn mới.
      priority:
        added: '%{user_name} thiết lập độ ưu tiên thành %{new_priority}'
        updated: '%{user_name} thay đổi mức độ ưu tiên từ %{old_priority} thành %{new_priority}'
        removed: '%{user_name} bỏ mức độ ưu tiên'
      assignee:
        self_assigned: '%{user_name} phân công chính mình vào hội thoại này'
        assigned: 'Chỉ định %{assignee_name} bởi %{user_name}'
        removed: 'Cuộc hội thoại chưa được chỉ định bởi %{user_name}'
      team:
        assigned: 'Chỉ định %{team_name} bởi %{user_name}'
        assigned_with_assignee: 'Phân công cho %{assignee_name} thông qua %{team_name} bởi %{user_name}'
        removed: 'Huỷ phân công cho %{team_name} bởi %{user_name}'
      labels:
        added: '%{user_name} thêm %{labels}'
        removed: '%{user_name} xoá %{labels}'
      sla:
        added: '%{user_name} added SLA policy %{sla_name}'
        removed: '%{user_name} removed SLA policy %{sla_name}'
      muted: '%{user_name} đã tắt tiếng hội thoại'
      unmuted: '%{user_name} đã bật tiếng cuộc trò chuyện'
      auto_resolution_message: 'Resolving the conversation as it has been inactive for a while. Please start a new conversation if you need further assistance.'
    templates:
      greeting_message_body: '%{account_name} thường trả lời trong vài giờ.'
      ways_to_reach_you_message_body: 'Trong lúc chờ đội ngũ hỗ trợ phản hồi, bạn hãy để lại email để nhận được thông báo nhanh nhất nhé.'
      email_input_box_message_body: 'Nhận thông báo qua email'
      csat_input_message_body: 'Bạn hãy vui lòng đánh giá hội thoại'
    reply:
      email:
        header:
          from_with_name: '%{assignee_name} từ %{inbox_name} <%{from_email}>'
          reply_with_name: '%{assignee_name} từ %{inbox_name} <reply+%{reply_email}>'
          friendly_name: '%{sender_name} từ %{business_name} <%{from_email}>'
          professional_name: '%{business_name} <%{from_email}>'
      channel_email:
        header:
          reply_with_name: '%{assignee_name} từ %{inbox_name} <%{from_email}>'
          reply_with_inbox_name: '%{inbox_name} <%{from_email}>'
      email_subject: 'Tin nhắn mới về cuộc trò chuyện này'
      transcript_subject: 'Bản ghi cuộc hội thoại'
    survey:
      response: 'Bạn hãy vui lòng đánh giá hội thoại, %{link}'
  contacts:
    online:
      delete: '%{contact_name} đang trực tiếng, vui lòng thử lại sau'
  integration_apps:
    dashboard_apps:
      name: 'Ứng dụng bảng điều khiển'
      description: 'Dashboard Apps allow you to create and embed applications that display user information, orders, or payment history, providing more context to your customer support agents.'
    dyte:
      name: 'Dyte'
      description: 'Dyte is a product that integrates audio and video functionalities into your application. With this integration, your agents can start video/voice calls with your customers directly from Livechat.'
      meeting_name: '%{agent_name} đã bắt đầu một cuộc họp'
    slack:
      name: 'Slack'
      description: "Integrate Livechat with Slack to keep your team in sync. This integration allows you to receive notifications for new conversations and respond to them directly within Slack's interface."
    webhooks:
      name: 'Webhooks'
      description: 'Webhook events provide real-time updates about activities in your Livechat account. You can subscribe to your preferred events, and Livechat will send you HTTP callbacks with the updates.'
    dialogflow:
      name: 'Dialogflow'
      description: 'Build chatbots with Dialogflow and easily integrate them into your inbox. These bots can handle initial queries before transferring them to a customer service agent.'
    google_translate:
      name: 'Google Translate'
      description: "Integrate Google Translate to help agents easily translate customer messages. This integration automatically detects the language and converts it to the agent's or admin's preferred language."
    openai:
      name: 'OpenAI'
      description: 'Leverage the power of large language models from OpenAI with the features such as reply suggestions, summarization, message rephrasing, spell-checking, and label classification.'
    linear:
      name: 'Linear'
      description: 'Create issues in Linear directly from your conversation window. Alternatively, link existing Linear issues for a more streamlined and efficient issue tracking process.'
    shopify:
      name: 'Shopify'
      description: 'Connect your Shopify store to access order details, customer information, and product data directly within your conversations and helps your support team provide faster, more contextual assistance to your customers.'
    leadsquared:
      name: 'LeadSquared'
      short_description: 'Sync your contacts and conversations with LeadSquared CRM.'
      description: 'Sync your contacts and conversations with LeadSquared CRM. This integration automatically creates leads in LeadSquared when new contacts are added, and logs conversation activity to provide your sales team with complete context.'
  captain:
    copilot_error: 'Please connect an assistant to this inbox to use Copilot'
    copilot_limit: 'You are out of Copilot credits. You can buy more credits from the billing section.'
  public_portal:
    search:
      search_placeholder: Tìm bài viết theo tiêu đề hoặc nội dung...
      empty_placeholder: Không tìm thấy kết quả.
      loading_placeholder: Đang tìm kiếm...
      results_title: Các kết quả tìm kiếm
    toc_header: 'Trên trang này'
    hero:
      sub_title: Tìm trong bài viết hoặc xem danh mục dưới đây.
    common:
      home: Trang Chủ
      last_updated_on: 'Cập nhật lần cuối: %{last_updated_on}'
      view_all_articles: View all
      article: bài viết
      articles: bài viết
      author: tác giả
      authors: authors
      other: other
      others: others
      by: By
      no_articles: Không tìm thấy bài viết
    footer:
      made_with: Tạo bởi
    header:
      go_to_homepage: Website
      appearance:
        system: System
        light: Light
        dark: Dark
      featured_articles: Featured Articles
      uncategorized: Chưa được phân loại
    404:
      title: Page not found
      description: We couldn't find the page you were looking for.
      back_to_home: Go to home page
  slack_unfurl:
    fields:
      name: Tên
      email: Email
      phone_number: Phone
      company_name: Công ty
      inbox_name: Hộp thư đến
      inbox_type: Inbox Type
    button: Mở cuộc trò chuyện
  time_units:
    days:
      other: '%{count} days'
    hours:
      other: '%{count} hours'
    minutes:
      other: '%{count} minutes'
    seconds:
      other: '%{count} seconds'
  automation:
    system_name: 'Automation System'
  crm:
    no_message: 'No messages in conversation'
    attachment: '[Attachment: %{type}]'
    no_content: '[No content]'
    created_activity: |
      New conversation started on %{brand_name}

      Channel: %{channel_info}
      Created: %{formatted_creation_time}
      Conversation ID: %{display_id}
      View in %{brand_name}: %{url}
    transcript_activity: |
      Conversation Transcript from %{brand_name}

      Channel: %{channel_info}
      Conversation ID: %{display_id}
      View in %{brand_name}: %{url}

      Transcript:
      %{format_messages}
